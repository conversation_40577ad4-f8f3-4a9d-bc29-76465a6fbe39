import _ from 'lodash';

export default async function (app) {
    // Sync partner.
    app.collection('kernel.partners').hooks({
        after: {
            update: [syncTransactionPartnerDetails],
            patch: [syncTransactionPartnerDetails]
        }
    });

    // await app.db.collection('accounting_transactions').updateMany(
    //     {},
    //     {
    //         $set: {
    //             reconciled: false,
    //             reconciliationCode: null,
    //             reconciliationData: null
    //         }
    //     },
    //     {
    //         collation: {locale: app.config('app.locale')}
    //     }
    // );
    // console.log('reset');
}

function syncTransactionPartnerDetails(context) {
    const app = context.app;

    (async () => {
        const result = Array.isArray(context.result) ? context.result : [context.result];
        const operations = [];
        const partnerIds = [];

        for (const item of result) {
            const data = _.pick(item, ['_id', 'code', 'name', 'type', 'identity', 'tin', 'groupId']);
            data.tinIdentity = !!item.tin ? item.tin : item.identity;
            data.tags = Array.isArray(item.tags) ? item.tags.map(tag => tag.label) : [];

            operations.push({
                updateMany: {
                    filter: {partnerId: item._id},
                    update: {
                        $set: {
                            partner: data
                        }
                    }
                }
            });
            partnerIds.push(item._id);
        }

        if (partnerIds.length > 0) {
            try {
                await app.rpc('accounting.partner-balances-sync', {partnerIds});
            } catch (error) {
                console.log('An error occurred while syncing accounting partner balances ', error.message);
            }
        }

        if (operations.length > 0) {
            await app.collection('accounting.transactions').bulkWrite(operations);
        }
    })();

    return context;
}

// await fixTransactionAccounts(app);
async function fixTransactionAccounts(app) {
    const unDeletedAccounts = await app.collection('kernel.accounts').find({
        deleted: {$ne: true}
    });
    const unDeletedAccountsMap = {};
    for (const unDeletedAccount of unDeletedAccounts) {
        unDeletedAccountsMap[unDeletedAccount._id] = unDeletedAccount;
    }
    const allAccounts = await app.collection('kernel.accounts').find({});
    const allAccountsMap = {};
    for (const allAccount of allAccounts) {
        allAccountsMap[allAccount._id] = allAccount;
    }
    const recoverAccountIds = [];

    const total = await app.collection('accounting.transactions').count({});
    const limit = 1000;
    let page = 0;
    let rowCount = 0;

    do {
        const filters = {
            $select: ['issueDate', 'voucherNo', 'accountId', 'account'],
            $sort: {issueDate: 1}
        };
        const transactionOperations = [];
        let result = null;

        // Adjust page.
        filters.$skip = page * limit;
        filters.$limit = limit;

        // Get rows.
        result = await app.collection('accounting.transactions').find(filters);

        for (const transaction of result) {
            const transactionAccountId = transaction.accountId;
            const transactionAccount = transaction.account;

            let unDeletedAccount = unDeletedAccountsMap[transactionAccountId];
            if (!!unDeletedAccount) {
                transactionOperations.push({
                    updateOne: {
                        filter: {_id: transaction._id},
                        update: {
                            $set: {
                                accountId: unDeletedAccount._id,
                                account: _.omit(unDeletedAccount, ['currency'])
                            }
                        }
                    }
                });

                rowCount++;

                continue;
            }

            unDeletedAccount = unDeletedAccounts.find(a => a.code === transactionAccount.code);
            if (!!unDeletedAccount) {
                transactionOperations.push({
                    updateOne: {
                        filter: {_id: transaction._id},
                        update: {
                            $set: {
                                accountId: unDeletedAccount._id,
                                account: _.omit(unDeletedAccount, ['currency'])
                            }
                        }
                    }
                });

                rowCount++;

                continue;
            }

            if (!recoverAccountIds.includes(transactionAccountId)) {
                recoverAccountIds.push(transactionAccountId);
            }

            rowCount++;
        }

        // Send progress percentage.
        if (total !== 0) {
            const percentage = (rowCount / total) * 100;
            console.log(`${percentage.toFixed(2)}%`);
        }

        if (transactionOperations.length > 0) {
            await app.collection('accounting.transactions').bulkWrite(transactionOperations);
        }

        page++;
    } while (total > 0 && rowCount < total);

    if (recoverAccountIds.length > 0) {
        await app.collection('kernel.accounts').bulkWrite([
            {
                updateMany: {
                    filter: {_id: {$in: recoverAccountIds}},
                    update: {
                        $set: {
                            deleted: false
                        },
                        $unset: {deletedAt: 1, deletedBy: 1}
                    }
                }
            }
        ]);
    }
}

// for (const entry of await app.collection('accounting.journal-entries').find()) {
//     let dt = 0;
//     let ct = 0;
//
//     for (const item of entry.items) {
//         dt+= item.debit;
//         ct+= item.credit;
//     }
//
//     dt = app.round(dt, 'currency');
//     ct = app.round(ct, 'currency');
//
//     if (dt !== ct) {
//         console.log(entry.voucherNo, dt, ct, dt-ct);
//     }
// }
