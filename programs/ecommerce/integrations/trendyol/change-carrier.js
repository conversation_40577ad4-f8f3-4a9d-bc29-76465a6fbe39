import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, orderId, carrierId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    const deliveryOption = store.deliveryOptions.find(sdo => sdo.carrierId === carrierId);
    if (!deliveryOption || !deliveryOption.integrationId) {
        log({
            level: 'error',
            message: app.translate('No delivery option found for the provided carrier!')
        });

        return;
    }

    const order = await app.collection('sale.orders').findOne({
        _id: orderId,
        $select: ['_id', 'code', 'integrationPayload', 'carrierId']
    });
    const providerCode = deliveryOption.integrationId.split('-')[1];

    await apiRequest({
        url,
        method: 'PUT',
        path: `order/sellers/${merchantId}/shipment-packages/${(order.integrationPayload ?? {}).packageId}/cargo-providers`,
        merchantId,
        apiKey,
        apiSecret,
        data: {
            cargoProvider: providerCode
        }
    });

    const orderNumber = order.code.replace(`${store.code}/`, '').trim();
    const result = await apiRequest({
        url,
        path: `order/sellers/${merchantId}/orders?orderNumber=${orderNumber}`,
        merchantId,
        apiKey,
        apiSecret
    });
    let integrationOrder = null;
    if (Array.isArray(result.content) && result.content.length > 0) {
        integrationOrder = result.content[0];
    } else if (_.isPlainObject(result.content)) {
        integrationOrder = result.content;
    }

    if (order.carrierId !== carrierId) {
        await app.collection('sale.orders').bulkWrite([
            {
                updateOne: {
                    filter: {_id: orderId},
                    update: {$set: {carrierId: carrierId}}
                }
            }
        ]);
    }

    if (
        !!integrationOrder &&
        !_.isUndefined(integrationOrder.cargoTrackingNumber) &&
        integrationOrder.cargoTrackingNumber !== null
    ) {
        return integrationOrder.cargoTrackingNumber.toString();
    }

    return null;
}
