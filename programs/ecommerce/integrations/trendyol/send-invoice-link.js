import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, invoiceId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    const invoice = await app.collection('accounting.customer-invoices').findOne({
        _id: invoiceId,
        $select: ['_id', 'integrationPayload', 'documentNo']
    });

    if (!!invoice && !!invoice.integrationPayload) {
        await apiRequest({
            url,
            method: 'POST',
            path: `sellers/${merchantId}/supplier-invoice-links`,
            merchantId,
            apiKey,
            apiSecret,
            data: {
                shipmentPackageId: invoice.integrationPayload.packageId,
                invoiceLink: app.absoluteUrl(`api/eops/invoices/preview/${invoiceId}`),
                ...(!_.isEmpty(invoice.documentNo) && {
                    invoiceDateTime: app.datetime.local().toMillis(),
                    invoiceNumber: invoice.documentNo
                })
            }
        });
    }
}
