import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, categoryId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;

    const result = await apiRequest({
        url,
        path: `product/product-categories/${categoryId}/attributes`,
        merchantId,
        apiKey,
        apiSecret
    });
    const categoryAttributes = result.categoryAttributes ?? [];
    const attributes = [];

    for (const categoryAttribute of categoryAttributes) {
        const attribute = {};

        attribute.id = categoryAttribute.attribute.id.toString();
        attribute.name = categoryAttribute.attribute.name;
        attribute.type =
            categoryAttribute.varianter === true || categoryAttribute.slicer === true ? 'variant' : 'feature';
        attribute.isCustom = categoryAttribute.allowCustom === true;
        attribute.isRequired = categoryAttribute.required === true;
        attribute.values = [];

        for (const attributeValue of categoryAttribute.attributeValues ?? []) {
            const value = {};

            value.id = attributeValue.id.toString();
            value.name = attributeValue.name;

            attribute.values.push(value);
        }

        attribute.values = _.orderBy(attribute.values, ['name'], ['asc']);

        attributes.push(attribute);
    }

    return _.orderBy(attributes, ['name'], ['asc']);
}
