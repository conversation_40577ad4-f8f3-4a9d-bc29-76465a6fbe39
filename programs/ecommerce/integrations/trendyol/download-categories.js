import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;

    await app.collection('ecommerce.categories').bulkWrite([{deleteMany: {filter: {storeId: store._id}}}], {
        ordered: false
    });

    const result = await apiRequest({
        url,
        path: 'product/product-categories',
        merchantId,
        apiKey,
        apiSecret
    });

    const operations = (function recursiveCategories(nodes, path = '') {
        const operations = [];
        for (const node of nodes) {
            const newPath = path ? `${path}/${node.name}` : node.name;

            if (node.subCategories?.length === 0) {
                operations.push({
                    insertOne: {
                        document: {
                            storeId: store._id,
                            integrationId: node.id.toString(),
                            name: newPath,
                            path: newPath,
                            isActive: true,
                            parentPath: '',
                            tree: {
                                depth: 0,
                                hasChild: false,
                                current: '',
                                path: '',
                                parent: ''
                            }
                        }
                    }
                });
            } else if (Array.isArray(node.subCategories) && node.subCategories.length > 0) {
                operations.push(...recursiveCategories(node.subCategories, newPath));
            }
        }
        return operations;
    })(result.categories);

    if (Array.isArray(operations) && operations.length > 0) {
        await app.collection('ecommerce.categories').bulkWrite(operations, {skipEvents: true});
    }

    onProgress(100);
}
