import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, startDate, endDate) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const user = await app.collection('kernel.users').findOne({isRoot: true});
    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        let start = app.datetime.local().minus({days: 5}).startOf('day').toJSDate().getTime();
        let end = app.datetime.local().endOf('day').toJSDate().getTime();

        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toJSDate().getTime();
            end = app.datetime.fromJSDate(endDate).endOf('day').toJSDate().getTime();
        }

        const result = await apiRequest({
            url,
            path: `order/sellers/${merchantId}/orders?status=Cancelled&startDate=${start}&endDate=${end}&page=${page}&orderByField=PackageLastModifiedDate&orderByDirection=DESC&size=50`,
            merchantId,
            apiKey,
            apiSecret
        });
        totalCount = result.totalElements;

        if (Array.isArray(result.content) && result.content.length > 0) {
            for (const integrationOrder of result.content) {
                try {
                    const code = `${store.code}/${integrationOrder.orderNumber}`;
                    const order = await app.collection('sale.orders').findOne({
                        code,
                        status: {$ne: 'canceled'},
                        $select: ['_id', 'code', 'transferIds', 'integrationPayload', 'relatedDocuments'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    if (!order) continue;

                    if ((await app.collection('sale.orders').count({code: `${order.code}-I`})) > 0) {
                        continue;
                    }

                    if (Array.isArray(order.relatedDocuments) && order.relatedDocuments.length > 0) {
                        const rd = order.relatedDocuments.find(rd => rd.collection === 'logistics.shipping-orders');

                        if (rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
                            try {
                                await app.rpc('logistics.shipping-orders-cancel', rd.ids[0]);
                            } catch (error) {
                                console.log(
                                    'Marketplace shipping order cancellation error in [import-canceled-orders] ->',
                                    error.message
                                );
                            }
                        }
                    }

                    const invoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code,
                        $select: ['_id']
                    });
                    if (invoice) {
                        const eArchiveInvoice = await app.collection('eops.e-archive-invoices').findOne({
                            invoiceId: invoice._id,
                            status: 'sent',
                            $select: ['_id']
                        });

                        if (eArchiveInvoice) {
                            await app.rpc('eops.e-archive-invoices-cancel', [eArchiveInvoice._id]);
                        }

                        await app.rpc('accounting.cancel-customer-invoice', invoice._id, {user});
                    }

                    if (order.transferIds.length > 0) {
                        const transfers = await app.collection('inventory.transfers').find({
                            _id: {$in: order.transferIds},
                            $select: ['_id'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        for (const transfer of transfers) {
                            await app.rpc('inventory.cancel-transfer', transfer._id, {user});
                        }
                    }

                    await app.rpc('sale.cancel-orders', [order._id], {user});

                    const isEveryLineItemMatching = order.integrationPayload.lines?.every(payloadLine => {
                        const matchingLine = integrationOrder.lines?.find(
                            line => parseInt(line.id) === parseInt(payloadLine.lineId)
                        );
                        return matchingLine && parseInt(matchingLine.quantity) === parseInt(payloadLine.quantity);
                    });

                    if (!isEveryLineItemMatching) {
                        await app.collection('sale.orders').bulkWrite([
                            {
                                updateOne: {
                                    filter: {_id: order._id},
                                    update: {
                                        $set: {
                                            code: `${order.code}-I`
                                        }
                                    }
                                }
                            }
                        ]);
                    }
                } catch (error) {
                    log({
                        level: 'error',
                        message: `${app.translate('An error occurred while importing order {{orderNumber}}!', {
                            orderNumber: integrationOrder.orderNumber
                        })} ${error.message}`
                    });
                }
            }

            page++;
            currentCount += result.content.length;

            await iterator();
        }
    })();
}
