import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    let page = 0;
    let currentCount = 0;
    let totalCount = 450000;

    await app.collection('ecommerce.brands').remove(
        {storeId: store._id},
        {
            disableSoftDelete: true,
            skipEvents: true
        }
    );

    await (async function iterator() {
        const result = await apiRequest({
            url,
            path: `product/brands?page=${page}&size=1000`,
            merchantId,
            apiKey,
            apiSecret
        });

        if (Array.isArray(result.brands) && result.brands.length > 0) {
            const operations = [];

            for (const brand of result.brands) {
                operations.push({
                    insertOne: {
                        document: {
                            storeId: store._id,
                            integrationId: brand.id.toString(),
                            name: brand.name,
                            isActive: true
                        }
                    }
                });
            }

            if (operations.length > 0) {
                await app.collection('ecommerce.brands').bulkWrite(operations, {
                    skipEvents: true
                });
            }

            page++;
            currentCount += result.brands.length;

            if (!!onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            await iterator();
        } else if (!!onProgress) {
            onProgress(100);
        }
    })();
}
