import {apiRequest} from './utils';

export default async function (app, store) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;

    await app.collection('ecommerce.store-transactions').remove({
        storeId: store._id,
        status: 'processing',
        date: {
            $lte: app.datetime.local().minus({days: 3}).startOf('day').toJSDate()
        }
    });

    const processingTransactions = await app.collection('ecommerce.store-transactions').find({
        storeId: store._id,
        status: 'processing'
    });
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const operations = [];

    for (const processingTransaction of processingTransactions) {
        try {
            const result = await apiRequest({
                url,
                method: 'GET',
                path: `product/sellers/${merchantId}/products/batch-requests/${processingTransaction.trackingId}`,
                merchantId,
                apiKey,
                apiSecret
            });
            const data = {
                status: 'processing',
                lastCheckDate: app.datetime.local().toJSDate(),
                lastTransactionDate: new Date(result.lastModification),
                errorsCount: 0,
                firstError: '',
                errors: []
            };

            // console.log(JSON.stringify(result, null, 2));

            if (Array.isArray(result.items) && result.items.length > 0) {
                const barcodes = result.items
                    .map(item => ((item.requestItem ?? {}).barcode ?? '').toString().trim())
                    .filter(barcode => !!barcode);
                const storeProductsMap = {};
                if (barcodes.length > 0) {
                    const storeProducts = await app.collection('ecommerce.store-products').find({
                        storeId: store._id,
                        productBarcode: {$in: barcodes}
                    });

                    for (const storeProduct of storeProducts) {
                        storeProductsMap[storeProduct.productBarcode] = storeProduct;
                    }
                }

                let isAllCompleted = true;
                for (const item of result.items) {
                    const barcode = ((item.requestItem ?? {}).barcode ?? '').toString().trim();
                    if (!barcode) continue;
                    const storeProduct = storeProductsMap[barcode];
                    if (!storeProduct) continue;

                    if (item.status === 'FAILED') {
                        for (const reason of item.failureReasons ?? []) {
                            const error = {};

                            error.productId = storeProduct.productId;
                            error.productCode = storeProduct.productCode;
                            error.productDefinition = storeProduct.productDefinition;
                            error.productBarcode = storeProduct.productBarcode;
                            error.message = reason;

                            data.errors.push(error);
                        }
                    }

                    if (!(item.status === 'FAILED' || item.status === 'SUCCESS')) {
                        isAllCompleted = false;
                    }
                }

                if (isAllCompleted) {
                    data.status = 'completed';
                }
                if (data.errors.length > 0) {
                    data.errorsCount = data.errors.length;
                    data.firstError = data.errors[0].message;
                }
            }

            if (data.status === 'processing' && result.status === 'COMPLETED') {
                data.status = 'completed';
            }

            operations.push({
                updateOne: {
                    filter: {_id: processingTransaction._id},
                    update: {
                        $set: data
                    }
                }
            });
        } catch (error) {
            const messages = [];
            if (error.response && !!error.response.data && Array.isArray(error.response.data.errors)) {
                for (const e of error.response.data.errors) {
                    messages.push(e.message);
                }
            } else {
                messages.push(error.message);
            }

            log({
                level: 'error',
                message: messages.join(' ')
            });
        }

        if (operations.length > 0) {
            await app.collection('ecommerce.store-transactions').bulkWrite(operations);
        }
    }
}
