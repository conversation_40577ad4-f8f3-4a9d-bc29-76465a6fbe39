import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, documentCollection, documentId, status) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    const document = await app.collection(documentCollection).findOne({
        _id: documentId,
        $select: ['_id', 'code', 'integrationPayload']
    });

    if (!!document && !!document.integrationPayload) {
        const payload = {
            status: 'Picking',
            lines: (document.integrationPayload.lines ?? []).map(line => ({
                lineId: parseInt(line.lineId),
                quantity: parseInt(line.quantity)
            })),
            params: {}
        };
        if (status === 'order-approved') {
            payload.status = 'Picking';
        } else if (status === 'invoice-approved') {
            payload.status = 'Invoiced';
            payload.params.invoiceNumber = document.code ?? '';
        }

        await apiRequest({
            url,
            method: 'PUT',
            path: `order/sellers/${merchantId}/shipment-packages/${document.integrationPayload.packageId}`,
            merchantId,
            apiKey,
            apiSecret,
            data: payload
        });
    }
}
