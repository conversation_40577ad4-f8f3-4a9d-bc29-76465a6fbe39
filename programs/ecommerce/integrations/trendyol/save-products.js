import {apiRequest} from './utils';
import _ from 'lodash';

export default async function (app, store, items) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, merchantId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const createPayload = [];
    const updatePayload = [];
    const storeProductOperations = [];

    for (const item of items) {
        const product = item.product;
        const storeProduct = item.storeProduct;
        const configurableProduct = item.configurableProduct;
        const units = item.units;
        const tax = item.tax;
        const row = {};

        // Get brand.
        let brandId = product.brandId;
        if (!brandId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Brand')
                    }
                )
            });
            continue;
        }
        brandId = ((store.brandMappings ?? []).find(m => m.erpBrandId === brandId) ?? {}).integrationBrandId;
        if (!brandId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. No mapping found for the {{field}} value!',
                    {
                        productName: product.definition,
                        field: app.translate('Brand')
                    }
                )
            });
            continue;
        }

        // Get category.
        let categoryId = product.categoryId;
        categoryId = ((store.categoryMappings ?? []).find(m => m.erpCategoryId === categoryId) ?? {})
            .integrationCategoryId;
        if (!categoryId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. No mapping found for the {{field}} value!',
                    {
                        productName: product.definition,
                        field: app.translate('Category')
                    }
                )
            });
            continue;
        }

        // Get barcode.
        let barcode = product.barcode;
        if (!barcode) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Barcode')
                    }
                )
            });
            continue;
        }

        // General.
        row.barcode = barcode;
        row.title = product.name;
        row.productMainId = (configurableProduct ?? {}).code ?? product.code;
        row.brandId = brandId;
        row.categoryId = categoryId;
        row.quantity = parseInt(storeProduct.quantity);
        row.stockCode = product.code;
        row.description = product.content ?? '';
        row.currencyType = 'TRY';
        row.vatRate = parseInt(tax.amount);

        // Prices.
        if (_.isFinite(storeProduct.discountedSalesPrice) && storeProduct.discountedSalesPrice > 0) {
            row.listPrice = storeProduct.salesPrice;
            row.salePrice = storeProduct.discountedSalesPrice;
        } else {
            row.listPrice = storeProduct.salesPrice;
            row.salePrice = storeProduct.salesPrice;
        }

        // Dimensional weight.
        let dimensionalWeight = 1;
        if (!!product.unitMeasurements && product.unitMeasurements.length > 0) {
            const m = product.unitMeasurements.find(m => m.unitId === item.unitId);
            const vwfWeightUnit = units.find(u => u.category === 'weight' && u.symbol === 'kg');
            const vwLengthUnit = units.find(u => u.category === 'length' && u.symbol === 'm');

            if (!!m) {
                let weight = m.grossWeight;
                let width = m.width;
                let height = m.height;
                let depth = m.depth;

                if (_.isNumber(weight) && _.isNumber(width) && _.isNumber(height) && _.isNumber(depth)) {
                    const weightUnit = units.find(u => u._id === m.grossWeightUnitId);
                    const widthUnit = units.find(u => u._id === m.widthUnitId);
                    const heightUnit = units.find(u => u._id === m.heightUnitId);
                    const depthUnit = units.find(u => u._id === m.depthUnitId);

                    if (!weightUnit) {
                        log({
                            level: 'error',
                            message: `Gross weight unit is not found! Product code: ${product.code}`
                        });
                        continue;
                    }
                    if (!widthUnit) {
                        log({
                            level: 'error',
                            message: `Width unit is not found! Product code: ${product.code}`
                        });
                        continue;
                    }
                    if (!heightUnit) {
                        log({
                            level: 'error',
                            message: `Height unit is not found! Product code: ${product.code}`
                        });
                        continue;
                    }
                    if (!depthUnit) {
                        log({
                            level: 'error',
                            message: `Depth unit is not found! Product code: ${product.code}`
                        });
                        continue;
                    }

                    if (weightUnit.type === 'smaller') weight /= weightUnit.ratio;
                    else if (weightUnit.type === 'bigger') weight *= weightUnit.ratio;
                    if (widthUnit.type === 'smaller') width /= widthUnit.ratio;
                    else if (widthUnit.type === 'bigger') width *= widthUnit.ratio;
                    if (heightUnit.type === 'smaller') height /= heightUnit.ratio;
                    else if (heightUnit.type === 'bigger') height *= heightUnit.ratio;
                    if (depthUnit.type === 'smaller') depth /= depthUnit.ratio;
                    else if (depthUnit.type === 'bigger') depth *= depthUnit.ratio;
                    if (vwfWeightUnit.type === 'smaller') weight *= vwfWeightUnit.ratio;
                    else if (vwfWeightUnit.type === 'bigger') weight /= vwfWeightUnit.ratio;
                    if (vwLengthUnit.type === 'smaller') width *= vwLengthUnit.ratio;
                    else if (vwLengthUnit.type === 'bigger') width /= vwLengthUnit.ratio;
                    if (vwLengthUnit.type === 'smaller') height *= vwLengthUnit.ratio;
                    else if (vwLengthUnit.type === 'bigger') height /= vwLengthUnit.ratio;
                    if (vwLengthUnit.type === 'smaller') depth *= vwLengthUnit.ratio;
                    else if (vwLengthUnit.type === 'bigger') depth /= vwLengthUnit.ratio;

                    dimensionalWeight = (width * height * depth) / 3000;
                    if (dimensionalWeight < weight) {
                        dimensionalWeight = weight;
                    }
                }
            }
        }
        row.dimensionalWeight = dimensionalWeight;

        // Cargo company.
        let deliveryOption = storeProduct.defaultDeliveryOptionId;
        // if (Array.isArray(product.ecommerceDeliveryOptionIds) && product.ecommerceDeliveryOptionIds.length > 0) {
        //     deliveryOption = (store.deliveryOptions ?? []).find(sdo =>
        //         product.ecommerceDeliveryOptionIds.includes(sdo.id)
        //     );
        // }
        if (!deliveryOption && !!store.defaultDeliveryOptionId) {
            deliveryOption = (store.deliveryOptions ?? []).find(sdo => sdo.id === store.defaultDeliveryOptionId);
        }
        if (!deliveryOption || !deliveryOption.integrationId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. No matching delivery option found!',
                    {
                        productName: product.definition
                    }
                )
            });
            continue;
        }
        row.cargoCompanyId = parseInt(deliveryOption.integrationId.split('-')[0]);

        // Delivery options.
        let deliveryDuration = null;
        if (!!product.ecommerceSameDayDelivery) {
            deliveryDuration = 1;
        }
        if (!deliveryDuration && _.isFinite(product.ecommerceEstimatedDeliveryDuration)) {
            deliveryDuration = product.ecommerceEstimatedDeliveryDuration;
        }
        if (!deliveryDuration && _.isFinite(store.estimatedDeliveryDuration)) {
            deliveryDuration = store.estimatedDeliveryDuration;
        }
        if (!deliveryDuration) {
            deliveryDuration = 3;
        }
        if (deliveryDuration === 1) {
            row.deliveryOption = {
                deliveryDuration: 1,
                fastDeliveryType: 'SAME_DAY_SHIPPING' // SAME_DAY_SHIPPING|FAST_DELIVERY
            };
        }
        // else {
        //     row.deliveryOption = {
        //         deliveryDuration: 1,
        //         fastDeliveryType: 'SAME_DAY_SHIPPING' // SAME_DAY_SHIPPING|FAST_DELIVERY
        //     };
        // }

        // Images.
        let images = [];
        if (Array.isArray(product.images) && product.images.length > 0) {
            images = product.images;
        }
        if (!!product.image) {
            images.unshift(product.image);
        }
        row.images = images.slice(0, 9).map(image => {
            return {url: app.absoluteUrl(`api/store/common/image/${image}-frs.png`)};
        });

        // Attributes.
        const attributes = [];
        if (!_.isEmpty(product.attributes) && Object.keys(product.attributes).length > 0) {
            const mappings = (store.attributeMappingsPayload ?? {})[categoryId];
            if (!mappings) {
                log({
                    level: 'error',
                    message: app.translate(
                        'The {{productName}} ERP product could not be published. No attribute mapping is found for the product category!',
                        {
                            productName: product.definition
                        }
                    )
                });
                continue;
            }
            const attributeMappings = mappings.attributeMappings ?? [];
            const valueMappings = mappings.valueMappings ?? [];

            for (const attributeCode of Object.keys(product.attributes)) {
                const erpValue = product.attributes[attributeCode];
                if (!erpValue || (typeof erpValue === 'string' && erpValue.trim() === '')) {
                    continue;
                }

                const attributeMapping = attributeMappings.find(
                    am => am.erpValue === attributeCode && am.type === 'variant'
                );
                if (!attributeMapping) {
                    // log({
                    //     level: 'error',
                    //     message: app.translate(
                    //         `The {{productName}} ERP product could not be published. No attribute mapping is found for the product attribute '{{attributeCode}}'!`,
                    //         {
                    //             productName: product.definition,
                    //             attributeCode
                    //         }
                    //     )
                    // });
                    //
                    // continue mainLoop;
                    continue;
                }

                if (!attributeMapping.isCustom) {
                    const valueMapping = valueMappings.find(
                        vm =>
                            vm.integrationAttributeValue === attributeMapping.integrationValue &&
                            vm.erpValue === erpValue
                    );
                    if (!valueMapping) {
                        // log({
                        //     level: 'error',
                        //     message: app.translate(
                        //         `The {{productName}} ERP product could not be published. No attribute value mapping is found for the product attribute value '{{attributeValue}}'!`,
                        //         {
                        //             productName: product.definition,
                        //             attributeValue: erpValue
                        //         }
                        //     )
                        // });
                        //
                        // continue mainLoop;
                        continue;
                    }

                    attributes.push({
                        attributeId: attributeMapping.integrationValue,
                        attributeValueId: valueMapping.integrationValue
                    });
                } else {
                    attributes.push({
                        attributeId: attributeMapping.integrationValue,
                        customAttributeValue: erpValue
                    });
                }
            }
        }
        if (!_.isEmpty(product.features) && Object.keys(product.features).length > 0) {
            const mappings = (store.attributeMappingsPayload ?? {})[categoryId];
            if (!mappings) {
                log({
                    level: 'error',
                    message: app.translate(
                        'The {{productName}} ERP product could not be published. No attribute mapping is found for the product category!',
                        {
                            productName: product.definition
                        }
                    )
                });
                continue;
            }
            const attributeMappings = mappings.attributeMappings ?? [];
            const valueMappings = mappings.valueMappings ?? [];

            for (const attributeCode of Object.keys(product.features)) {
                const erpValue = product.features[attributeCode];
                if (!erpValue || (typeof erpValue === 'string' && erpValue.trim() === '')) {
                    continue;
                }

                const attributeMapping = attributeMappings.find(
                    am => am.erpValue === attributeCode && am.type === 'feature'
                );
                if (!attributeMapping) {
                    // log({
                    //     level: 'error',
                    //     message: app.translate(
                    //         `The {{productName}} ERP product could not be published. No attribute mapping is found for the product attribute '{{attributeCode}}'!`,
                    //         {
                    //             productName: product.definition,
                    //             attributeCode
                    //         }
                    //     )
                    // });
                    //
                    // continue mainLoop;
                    continue;
                }

                if (!attributeMapping.isCustom) {
                    const valueMapping = valueMappings.find(
                        vm =>
                            vm.integrationAttributeValue === attributeMapping.integrationValue &&
                            vm.erpValue === erpValue
                    );
                    if (!valueMapping) {
                        // log({
                        //     level: 'error',
                        //     message: app.translate(
                        //         `The {{productName}} ERP product could not be published. No attribute value mapping is found for the product attribute value '{{attributeValue}}'!`,
                        //         {
                        //             productName: product.definition,
                        //             attributeValue: erpValue
                        //         }
                        //     )
                        // });
                        //
                        // continue mainLoop;
                        continue;
                    }

                    attributes.push({
                        attributeId: attributeMapping.integrationValue,
                        attributeValueId: valueMapping.integrationValue
                    });
                } else {
                    attributes.push({
                        attributeId: attributeMapping.integrationValue,
                        customAttributeValue: erpValue
                    });
                }
            }
        }
        row.attributes = attributes;

        // On sale.
        if (storeProduct.onSale === false) {
            row.quantity = 0;
            row.listPrice = 0;
            row.salePrice = 0;
        }

        if (!storeProduct.integrationId) {
            createPayload.push(row);
        } else {
            updatePayload.push(row);
        }

        storeProductOperations.push({
            updateOne: {
                filter: {_id: storeProduct._id},
                update: {
                    $set: {
                        integrationStatus: 'waiting',
                        isPublished: true,
                        publishedAt: app.datetime.local().toJSDate()
                    }
                }
            }
        });
    }

    let hasError = false;
    if (createPayload.length > 0) {
        try {
            const result = await apiRequest({
                url,
                method: 'POST',
                path: `product/sellers/${merchantId}/products`,
                merchantId,
                apiKey,
                apiSecret,
                data: {
                    items: createPayload
                }
            });

            if (!!result.batchRequestId) {
                const transaction = {};
                transaction.storeId = store._id;
                transaction.trackingId = result.batchRequestId;
                transaction.status = 'processing';
                transaction.transactionType = 'product-creation';
                transaction.date = app.datetime.local().toJSDate();
                await app.collection('ecommerce.store-transactions').create(transaction);
            }
        } catch (error) {
            hasError = true;

            const messages = [];
            if (error.response && !!error.response.data && Array.isArray(error.response.data.errors)) {
                for (const e of error.response.data.errors) {
                    messages.push(e.message);
                }
            } else {
                messages.push(error.message);
            }

            log({
                level: 'error',
                message: messages.join(' ')
            });
        }
    }

    if (updatePayload.length > 0) {
        try {
            const result = await apiRequest({
                url,
                method: 'PUT',
                path: `product/sellers/${merchantId}/products`,
                merchantId,
                apiKey,
                apiSecret,
                data: {
                    items: updatePayload
                }
            });

            if (!!result.batchRequestId) {
                const transaction = {};
                transaction.storeId = store._id;
                transaction.trackingId = result.batchRequestId;
                transaction.status = 'processing';
                transaction.transactionType = 'product-update';
                transaction.date = app.datetime.local().toJSDate();
                await app.collection('ecommerce.store-transactions').create(transaction);
            }
        } catch (error) {
            hasError = true;

            const messages = [];
            if (error.response && !!error.response.data && Array.isArray(error.response.data.errors)) {
                for (const e of error.response.data.errors) {
                    messages.push(e.message);
                }
            } else {
                messages.push(error.message);
            }

            log({
                level: 'error',
                message: messages.join(' ')
            });
        }
    }

    if (storeProductOperations.length > 0 && !hasError) {
        await app.collection('ecommerce.store-products').bulkWrite(storeProductOperations);
    }
}
