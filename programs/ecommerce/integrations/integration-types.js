export default [
    {
        name: 'enterstore',
        title: 'EnterStore',
        logo: '/static/images/ecommerce-integration-logos/enterstore.png',
        params: {
            apiSecret: ''
        },
        needsCargoCompanyMappings: false,
        needsProductCategoryMappings: false,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: false,
        needsProductFeatureMappings: false,
        onlyLeafCategories: false,
        attributesAreCategoryBased: false,
        allowedCurrencies: 'all'
    },
    {
        name: 'shopify',
        title: 'Shopify',
        logo: '/static/images/ecommerce-integration-logos/shopify.png',
        params: {
            shopName: '',
            accessToken: '',
            orderTagsToFilter: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: false,
        needsProductPaymentMethodMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: false,
        needsProductFeatureMappings: false,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        barcodeMatchingOnly: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'trendyol',
        title: 'Trendyol',
        logo: '/static/images/ecommerce-integration-logos/trendyol.png',
        params: {
            url: 'https://apigw.trendyol.com/integration',
            merchantId: '',
            apiKey: '',
            apiSecret: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: true,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'hepsiburada',
        title: 'Hepsiburada',
        logo: '/static/images/ecommerce-integration-logos/hepsi-burada.png',
        params: {
            merchantId: '',
            apiKey: '',
            apiSecret: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'pazarama',
        title: 'Pazarama',
        logo: '/static/images/ecommerce-integration-logos/pazarama.png',
        params: {
            apiKey: '',
            apiSecret: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: true,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'pttavm',
        title: 'PttAvm',
        logo: '/static/images/ecommerce-integration-logos/pttavm.png',
        params: {
            merchantId: '',
            username: '',
            password: ''
        },
        needsCargoCompanyMappings: false,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: false,
        needsProductFeatureMappings: false,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'n11',
        title: 'n11',
        logo: '/static/images/ecommerce-integration-logos/n11.png',
        params: {
            apiKey: '',
            apiSecret: '',
            shippingTemplate: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'ciceksepeti',
        title: 'ÇiçekSepeti',
        logo: '/static/images/ecommerce-integration-logos/ciceksepeti.png',
        params: {
            apiKey: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'koctas',
        title: 'Koçtaş',
        logo: '/static/images/ecommerce-integration-logos/koctas.png',
        params: {
            url: '',
            apiKey: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: false,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    },
    {
        name: 'boyner',
        title: 'Boyner',
        logo: '/static/images/ecommerce-integration-logos/boyner.png',
        params: {
            url: 'https://merchantapi.boyner.com.tr',
            supplierId: '',
            apiKey: '',
            apiSecret: ''
        },
        needsCargoCompanyMappings: true,
        needsProductCategoryMappings: true,
        needsProductBrandMappings: true,
        needsProductAttributeMappings: true,
        needsProductFeatureMappings: true,
        onlyLeafCategories: true,
        attributesAreCategoryBased: true,
        allowedCurrencies: 'all'
    }
    // {
    //     name: 'farmazon',
    //     title: 'Farmazon',
    //     logo: '/static/images/ecommerce-integration-logos/koctas.png',
    //     params: {
    //         url: '',
    //         apiKey: ''
    //     },
    //     // needsCargoCompanyMappings: true,
    //     // needsProductCategoryMappings: true,
    //     // needsProductBrandMappings: false,
    //     // needsProductAttributeMappings: true,
    //     // needsProductFeatureMappings: true,
    //     // onlyLeafCategories: true,
    //     // attributesAreCategoryBased: true,
    //     allowedCurrencies: ['try']
    // }
];
